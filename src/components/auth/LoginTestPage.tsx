import { useState } from 'react'
import { ArrowLeft, LogIn, UserPlus } from 'lucide-react'

interface LoginTestPageProps {
  onBack: () => void
  onLogin: () => void
  onRegister: () => void
}

export function LoginTestPage({ onBack, onLogin, onRegister }: LoginTestPageProps) {
  const [testMessage, setTestMessage] = useState('')

  const handleTestClick = () => {
    setTestMessage('登录页面功能正常！点击事件已触发。')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
      <div className="container mx-auto px-4 py-8">
        {/* 顶部导航 */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>返回</span>
          </button>
          <h1 className="text-2xl font-bold text-gray-800">登录功能测试</h1>
          <div className="w-16"></div>
        </div>

        {/* 测试区域 */}
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
            <div className="text-center space-y-6">
              <h2 className="text-xl font-semibold text-gray-800">
                🔧 登录功能测试页面
              </h2>
              
              <p className="text-gray-600">
                这是一个测试页面，用于验证登录相关功能是否正常工作。
              </p>

              {/* 测试按钮 */}
              <button
                onClick={handleTestClick}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg font-medium transition-colors"
              >
                点击测试页面响应
              </button>

              {testMessage && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <p className="text-green-600 text-sm">{testMessage}</p>
                </div>
              )}

              {/* 登录注册按钮 */}
              <div className="space-y-3">
                <button
                  onClick={onLogin}
                  className="w-full flex items-center justify-center space-x-2 bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg font-medium transition-colors"
                >
                  <LogIn className="w-5 h-5" />
                  <span>进入正式登录页面</span>
                </button>

                <button
                  onClick={onRegister}
                  className="w-full flex items-center justify-center space-x-2 bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg font-medium transition-colors"
                >
                  <UserPlus className="w-5 h-5" />
                  <span>进入注册页面</span>
                </button>
              </div>

              {/* 说明文字 */}
              <div className="text-left bg-gray-50 rounded-lg p-4">
                <h3 className="font-medium text-gray-800 mb-2">测试说明：</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• 如果能看到这个页面，说明路由正常</li>
                  <li>• 如果能点击按钮，说明交互正常</li>
                  <li>• 如果能返回首页，说明导航正常</li>
                  <li>• 测试账号：手机 13800138000，验证码 123456</li>
                  <li>• 测试账号：邮箱 <EMAIL>，密码 123456</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
