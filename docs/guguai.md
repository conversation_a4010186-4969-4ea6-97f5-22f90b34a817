API 参考
REST APIJSONBearer Authentication
我们的 API 遵循 RESTful 服务的标准实践。所有 API 端点都以 'https://api.gugudata.ai/v1' 为前缀，并需要使用您的 API 密钥进行身份验证。

基础 URL
https://api.gugudata.ai/v1
身份验证
所有 API 请求都需要使用您的 GuGuData AI API 密钥进行身份验证。您需要在所有请求的 Authorization 头中包含您的 API 密钥。

Authorization 头
Authorization: Bearer <GuGuData AI API Key>
将 <GuGuData AI API Key> 替换为您的实际 API 密钥。请妥善保管您的 API 密钥，不要在客户端代码中暴露。

端点
POST
/chat/completions
创建聊天完成
为聊天消息创建完成

请求参数
参数	类型	必需	描述
model	string	是	要使用的模型 ID。目前仅支持 'gugudata-chat'。
messages	array	是	包含对话的消息列表。
stream	boolean	否	如果设置为 true，将发送部分消息增量，类似于 ChatGPT。默认为 false。
请求示例
curl https://api.gugudata.ai/v1/chat/completions \
-H "Content-Type: application/json" \
-H "Authorization: Bearer <GuGuData AI API Key>" \
-d '{
"model": "gugudata-chat",
"messages": [
{"role": "system", "content": "You are a helpful assistant."},
{"role": "user", "content": "What is the next solar term in the 24 solar terms?"}
],
"stream": false
}'
响应示例
{
"id": "chatcmpl-123456789",
"object": "chat.completion",
"created": 1677858242,
"model": "gugudata-chat",
"choices": [
{
"message": {
"role": "assistant",
"content": "The next solar term in the 24 solar terms is Minor Heat (小暑, Xiǎoshǔ), which usually begins around July 7th when the sun reaches the celestial longitude of 105°."
},
"finish_reason": "stop",
"index": 0
}
],
"usage": {
"prompt_tokens": 25,
"completion_tokens": 42,
"total_tokens": 67
}
}